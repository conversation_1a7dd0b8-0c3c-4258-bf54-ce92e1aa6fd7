{"name": "safety-dashboard-client", "version": "2.0.0", "private": true, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^5.15.1", "@mui/material": "^5.17.1", "@tanstack/react-query": "^5.80.6", "axios": "^1.6.2", "chart.js": "^4.4.9", "chartjs-adapter-date-fns": "^3.0.0", "chartjs-plugin-datalabels": "^2.2.0", "date-fns": "^2.30.0", "echarts": "^5.6.0", "echarts-for-react": "^3.0.2", "framer-motion": "^12.16.0", "jspdf": "^3.0.1", "plotly.js": "^3.0.1", "react": "^18.2.0", "react-beautiful-dnd": "^13.1.1", "react-chartjs-2": "^5.3.0", "react-dom": "^18.2.0", "react-plotly.js": "^2.6.0", "react-router-dom": "^6.20.1", "react-scripts": "5.0.1", "recharts": "^2.8.0", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}