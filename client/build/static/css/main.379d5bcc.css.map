{"version": 3, "file": "static/css/main.379d5bcc.css", "mappings": "kGAOA,KAEE,yIAEY,CAIZ,eAAgB,CAChB,eACF,CAEA,KACE,+EAEF,CAGA,oBAEE,UAAW,CADX,SAEF,CAEA,0BACE,oBAA+B,CAC/B,iBACF,CAEA,0BACE,oBAAgC,CAChC,iBACF,CAEA,gCACE,oBACF,CAGA,4CACE,oBACF,CAEA,4CACE,oBACF,CAEA,kDACE,gBACF,CAGA,eACE,2BAAuC,CACvC,kBACF,CAEA,iCACE,2BAA0C,CAC1C,kBACF,CAGA,EACE,gHAEF,CAGA,YACE,0BAAsC,CACtC,aACF,CAEA,iBACE,0BAAsC,CACtC,aACF,CAGA,8BACE,sBAA0C,CAC1C,UACF,CAEA,mCACE,sBAA0C,CAC1C,UACF,CAGA,mDAEE,gBACF,CAGA,uBACE,wBAAyB,CACzB,aACF,CCvGA,EACE,qBACF,CAEA,KAKE,kCAAmC,CACnC,iCAAkC,CAClC,wBAAyB,CALzB,mIAEY,CAHZ,QAOF,CAGA,UAEE,wBAAiB,CAAjB,gBACF,CAEA,sBAJE,yBAQF,CAJA,YAGE,mBACF,CAGA,iBACE,uBACF,CAEA,uBAEE,qCAA+C,CAD/C,0BAEF,CAEA,wBACE,yBACF,CAGA,WAEE,oCAAoD,CADpD,mCAEF,CAEA,kBAME,oBAAmC,CAGnC,iBAAkB,CAFlB,UAAY,CANZ,mBAAoB,CASpB,cAAe,CACf,eAAgB,CAPhB,QAAS,CAIT,gBAAiB,CAKjB,mBAAoB,CAXpB,iBAAkB,CAClB,OAAQ,CAER,8BAAgC,CAOhC,YAEF,CAGA,YAOE,mBAAqB,CANrB,wBAAyB,CAGzB,gBAAiB,CAEjB,4BAEF,CAGA,4EAKE,yBAA4B,CAC5B,uBAAyB,CAFzB,mBAAqB,CADrB,4BAIF,CAGA,wBAEE,kCACF,CAEA,uDAJE,yBAMF,CAGA,iBAEE,eAAgB,CADhB,iBAEF,CAMA,qEACE,mBACF,CAGA,EACE,uBACF,CAGA,oBAEE,UAAW,CADX,SAEF,CAEA,0BACE,kBAAmB,CACnB,iBACF,CAEA,0BACE,kBAAmB,CACnB,iBACF,CAEA,gCACE,kBACF,CAGA,iBACE,MACE,SACF,CACA,IACE,UACF,CACF,CAEA,OACE,mDACF,CAGA,kBACE,GACE,SAAU,CACV,0BACF,CACA,GACE,SAAU,CACV,uBACF,CACF,CAEA,SACE,6BACF,CAGA,kBAEE,gEAAqF,CADrF,0BAEF,CAGA,uBACE,yBAA0B,CAC1B,iBACF,CAEA,6BAKE,kBAAmB,CAGnB,iBAAkB,CAFlB,UAAY,CALZ,yBAA0B,CAQ1B,cAAe,CACf,eAAgB,CAChB,SAAU,CAJV,eAAgB,CALhB,iBAAkB,CAElB,UAAW,CADX,SAAU,CASV,2BACF,CAEA,mCACE,SACF,CAGA,yBACE,YACE,kBACF,CAEA,6BACE,YACF,CACF,CAGA,uCACE,EACE,kCAAqC,CACrC,qCAAuC,CACvC,mCACF,CACF,CAGA,+BACE,YACE,qBACF,CAEA,uBACE,sBACF,CACF,CAkDA,0zBAIE,2BAA6B,CAG7B,uCAAyC,CACzC,wCAA0C,CAH1C,oCAAsC,CACtC,qCAGF,CAGA,mEAOE,kCAAwC,CALxC,qBAAuB,CAEvB,yBAA2B,CAK3B,oCAAqC,CAHrC,kBAAoB,CAEpB,uBAAwB,CALxB,sBAAwB,CAExB,mBAKF,CAGA,2BAEE,0BAAkC,CAElC,8DAAgE,CAIhE,mDAAqD,CACrD,qCAAuC,CAHvC,iCAAmC,CAKnC,uDACF,CAEA,iCAEE,6DAA+D,CAD/D,+BAEF", "sources": ["index.css", "styles/globals.css"], "sourcesContent": ["/* Modern SafetyConnect Styles */\n@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');\n\n* {\n  box-sizing: border-box;\n}\n\nbody {\n  margin: 0;\n  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',\n    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',\n    sans-serif;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  background-color: #f8fafc;\n  font-weight: 400;\n  line-height: 1.5;\n}\n\ncode {\n  font-family: 'SF Mono', source-code-pro, Menlo, Monaco, Consolas, 'Courier New',\n    monospace;\n}\n\n/* Modern scrollbar - Light mode */\n::-webkit-scrollbar {\n  width: 6px;\n  height: 6px;\n}\n\n::-webkit-scrollbar-track {\n  background: rgba(0, 0, 0, 0.05);\n  border-radius: 3px;\n}\n\n::-webkit-scrollbar-thumb {\n  background: rgba(9, 47, 87, 0.3);\n  border-radius: 3px;\n}\n\n::-webkit-scrollbar-thumb:hover {\n  background: rgba(9, 47, 87, 0.5);\n}\n\n/* Dark mode scrollbar */\n[data-theme=\"dark\"] ::-webkit-scrollbar-track {\n  background: rgba(255, 255, 255, 0.1);\n}\n\n[data-theme=\"dark\"] ::-webkit-scrollbar-thumb {\n  background: rgba(255, 255, 255, 0.3);\n}\n\n[data-theme=\"dark\"] ::-webkit-scrollbar-thumb:hover {\n  background: rgba(255, 255, 255, 0.4);\n}\n\n/* Modern focus styles */\n*:focus-visible {\n  outline: 2px solid rgba(9, 47, 87, 0.5);\n  outline-offset: 2px;\n}\n\n[data-theme=\"dark\"] *:focus-visible {\n  outline: 2px solid rgba(59, 130, 246, 0.5);\n  outline-offset: 2px;\n}\n\n/* Smooth transitions for all elements */\n* {\n  transition: color 0.2s ease, background-color 0.2s ease, border-color 0.2s ease,\n              box-shadow 0.2s ease, transform 0.2s ease;\n}\n\n/* Modern selection styles - Light mode */\n::selection {\n  background-color: rgba(9, 47, 87, 0.2);\n  color: #092f57;\n}\n\n::-moz-selection {\n  background-color: rgba(9, 47, 87, 0.2);\n  color: #092f57;\n}\n\n/* Dark mode selection styles */\n[data-theme=\"dark\"] ::selection {\n  background-color: rgba(255, 255, 255, 0.2);\n  color: #ffffff;\n}\n\n[data-theme=\"dark\"] ::-moz-selection {\n  background-color: rgba(255, 255, 255, 0.2);\n  color: #ffffff;\n}\n\n/* Ensure proper cursor visibility in dark mode */\n[data-theme=\"dark\"] input,\n[data-theme=\"dark\"] textarea {\n  caret-color: #ffffff;\n}\n\n/* Dark mode body background */\n[data-theme=\"dark\"] body {\n  background-color: #0f172a;\n  color: #f1f5f9;\n}\n\n/* AI Insights Loading Animation */\n@keyframes pulse {\n  0%, 100% {\n    opacity: 1;\n    transform: scale(1);\n  }\n  50% {\n    opacity: 0.5;\n    transform: scale(1.1);\n  }\n}\n", "/* Global Styles for Safety Connect Dashboard */\n\n/* Reset and base styles */\n* {\n  box-sizing: border-box;\n}\n\nbody {\n  margin: 0;\n  font-family: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Oxygen',\n    'Ubuntu', 'Can<PERSON><PERSON>', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',\n    sans-serif;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  background-color: #f8fafc;\n}\n\n/* Custom Dashboard Drag and Drop Styles */\n.dragging {\n  cursor: grabbing !important;\n  user-select: none;\n}\n\n.dragging * {\n  cursor: grabbing !important;\n  /* Keep pointer events enabled for chart visibility */\n  pointer-events: auto;\n}\n\n/* Chart container drag styles */\n.chart-draggable {\n  transition: all 0.2s ease;\n}\n\n.chart-draggable:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);\n}\n\n.chart-draggable:active {\n  cursor: grabbing !important;\n}\n\n/* Drag drop zone indicators */\n.drag-over {\n  border: 2px dashed #3b82f6 !important;\n  background-color: rgba(59, 130, 246, 0.1) !important;\n}\n\n.drag-over::before {\n  content: \"Drop here\";\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  background: rgba(59, 130, 246, 0.9);\n  color: white;\n  padding: 8px 16px;\n  border-radius: 4px;\n  font-size: 14px;\n  font-weight: 500;\n  z-index: 1000;\n  pointer-events: none;\n}\n\n/* Disable text selection during drag but keep chart visibility */\n.dragging * {\n  -webkit-user-select: none;\n  -moz-user-select: none;\n  -ms-user-select: none;\n  user-select: none;\n  /* Ensure charts remain visible during drag */\n  visibility: visible !important;\n  opacity: 1 !important;\n}\n\n/* Specific rules for chart components during drag */\n.dragging .plotly,\n.dragging .echarts-for-react,\n.dragging [data-chart-index] {\n  visibility: visible !important;\n  opacity: 1 !important;\n  background: white !important;\n  color: inherit !important;\n}\n\n/* Ensure chart containers maintain their appearance during drag */\n.dragging .MuiCard-root {\n  background: white !important;\n  border: 1px solid #e5e7eb !important;\n}\n\n.dragging .MuiCardContent-root {\n  background: white !important;\n}\n\n/* Chart interaction styles */\n.chart-container {\n  position: relative;\n  overflow: hidden;\n}\n\n.chart-container.edit-mode {\n  pointer-events: auto; /* Keep charts interactive in edit mode */\n}\n\n.chart-container.edit-mode .chart-content {\n  pointer-events: auto; /* Keep chart content visible and interactive */\n}\n\n/* Smooth transitions for all interactive elements */\n* {\n  transition: all 0.2s ease;\n}\n\n/* Custom scrollbar */\n::-webkit-scrollbar {\n  width: 8px;\n  height: 8px;\n}\n\n::-webkit-scrollbar-track {\n  background: #f1f1f1;\n  border-radius: 4px;\n}\n\n::-webkit-scrollbar-thumb {\n  background: #c1c1c1;\n  border-radius: 4px;\n}\n\n::-webkit-scrollbar-thumb:hover {\n  background: #a8a8a8;\n}\n\n/* Loading animations */\n@keyframes pulse {\n  0%, 100% {\n    opacity: 1;\n  }\n  50% {\n    opacity: 0.5;\n  }\n}\n\n.pulse {\n  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;\n}\n\n/* Fade in animation */\n@keyframes fadeIn {\n  from {\n    opacity: 0;\n    transform: translateY(20px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n.fade-in {\n  animation: fadeIn 0.5s ease-out;\n}\n\n/* Chart hover effects */\n.chart-card:hover {\n  transform: translateY(-4px);\n  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);\n}\n\n/* Edit mode visual indicators */\n.edit-mode .chart-card {\n  border: 2px dashed #e0e7ff;\n  position: relative;\n}\n\n.edit-mode .chart-card::after {\n  content: \"Drag to reorder\";\n  position: absolute;\n  top: -10px;\n  right: 10px;\n  background: #059669;\n  color: white;\n  padding: 4px 8px;\n  border-radius: 4px;\n  font-size: 10px;\n  font-weight: 500;\n  opacity: 0;\n  transition: opacity 0.2s ease;\n}\n\n.edit-mode .chart-card:hover::after {\n  opacity: 1;\n}\n\n/* Responsive design improvements */\n@media (max-width: 768px) {\n  .chart-card {\n    margin-bottom: 16px;\n  }\n  \n  .edit-mode .chart-card::after {\n    display: none;\n  }\n}\n\n/* Accessibility improvements */\n@media (prefers-reduced-motion: reduce) {\n  * {\n    animation-duration: 0.01ms !important;\n    animation-iteration-count: 1 !important;\n    transition-duration: 0.01ms !important;\n  }\n}\n\n/* High contrast mode support */\n@media (prefers-contrast: high) {\n  .chart-card {\n    border: 2px solid #000;\n  }\n  \n  .edit-mode .chart-card {\n    border: 3px dashed #000;\n  }\n}\n\n/* Chart container sharp corners override */\n.chart-container,\n.chart-container *,\n[data-chart-index],\n[data-chart-index] *,\n.plotly,\n.plotly *,\n.echarts-for-react,\n.echarts-for-react *,\n.MuiCard-root[data-chart-index],\n.MuiCard-root[data-chart-index] *,\n.MuiCardContent-root[data-chart-index],\n.MuiCardContent-root[data-chart-index] * {\n  border-radius: 2px !important;\n  border-top-left-radius: 2px !important;\n  border-top-right-radius: 2px !important;\n  border-bottom-left-radius: 2px !important;\n  border-bottom-right-radius: 2px !important;\n}\n\n/* Dashboard chart specific overrides */\n[data-dashboard-container] .MuiCard-root,\n[data-dashboard-container] .MuiCardContent-root,\n[data-dashboard-container] .plotly,\n[data-dashboard-container] .echarts-for-react,\n[data-dashboard-container] [data-chart-index],\n[data-dashboard-container] [data-chart-index] * {\n  border-radius: 2px !important;\n  border-top-left-radius: 2px !important;\n  border-top-right-radius: 2px !important;\n  border-bottom-left-radius: 2px !important;\n  border-bottom-right-radius: 2px !important;\n}\n\n/* Main dashboard specific overrides */\n.MuiGrid-container .MuiCard-root,\n.MuiGrid-container .MuiCardContent-root,\n.MuiGrid-container .echarts-for-react,\n.MuiGrid-container .plotly,\n.MuiGrid-container .MuiAlert-root {\n  border-radius: 2px !important;\n  border-top-left-radius: 2px !important;\n  border-top-right-radius: 2px !important;\n  border-bottom-left-radius: 2px !important;\n  border-bottom-right-radius: 2px !important;\n}\n\n/* KPI cards and chart cards specific overrides */\n.motion-div .MuiCard-root,\n.motion-div .MuiCardContent-root,\n.motion-div .echarts-for-react,\n.motion-div .plotly {\n  border-radius: 2px !important;\n  border-top-left-radius: 2px !important;\n  border-top-right-radius: 2px !important;\n  border-bottom-left-radius: 2px !important;\n  border-bottom-right-radius: 2px !important;\n}\n\n/* AI Icon specific styles - ensure no borders */\nimg[src*=\"ai-insights-icon.gif\"],\nimg[alt*=\"AI Insights Assistant\"] {\n  border: none !important;\n  outline: none !important;\n  box-shadow: none !important;\n  padding: 0 !important;\n  margin: 0 !important;\n  background-color: transparent !important;\n  mix-blend-mode: multiply; /* Helps blend away white background */\n  filter: brightness(1.1) contrast(1.1); /* Enhance the icon appearance */\n}\n\n/* Enhanced AI Insights Icon styles for better white background removal */\n.ai-insights-icon-enhanced {\n  /* Remove any white background from the GIF */\n  background: transparent !important;\n  /* Improve icon visibility and contrast */\n  filter: brightness(1.05) contrast(1.15) saturate(1.1) !important;\n  /* Better blending to remove white areas */\n  mix-blend-mode: multiply !important;\n  /* Ensure smooth rendering */\n  image-rendering: -webkit-optimize-contrast !important;\n  image-rendering: crisp-edges !important;\n  /* Animation for hover effect */\n  transition: transform 0.2s ease, filter 0.2s ease !important;\n}\n\n.ai-insights-icon-enhanced:hover {\n  transform: scale(1.35) !important;\n  filter: brightness(1.1) contrast(1.2) saturate(1.15) !important;\n}\n"], "names": [], "sourceRoot": ""}