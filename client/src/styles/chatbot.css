/**
 * <PERSON><PERSON><PERSON> Styles
 * Modern styling for the SafetyConnect AI chatbot
 */

/* Enhanced Chatbot Container - Completely isolated positioning */
.chatbot-container {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  /* Ensure complete isolation from parent containers */
  position: fixed !important;
  z-index: 9999 !important;
  /* Prevent chatbot from affecting page scroll */
  pointer-events: auto;
  contain: layout style;
  /* Ensure it doesn't inherit any transform that would break fixed positioning */
  transform: none !important;
  /* Prevent any parent transforms from affecting this element */
  will-change: auto !important;
  /* Force positioning relative to viewport only */
  isolation: isolate;
  /* Ensure no parent overflow affects this */
  clip-path: none !important;
}

/* Enhanced Chat Toggle Button with Improved Animations */
.chatbot-toggle {
  position: fixed !important;
  bottom: 24px !important;
  right: 24px !important;
  z-index: 9999 !important;
  /* Prevent scroll interference */
  pointer-events: auto;
  contain: layout;
  /* Ensure complete isolation */
  isolation: isolate;
  transform: none !important;
  will-change: auto !important;
}

.chatbot-toggle button {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
  box-shadow: 0 8px 32px rgba(9, 47, 87, 0.3) !important;
  transform: none !important;
  background: linear-gradient(135deg, #092f57 0%, #1976d2 100%) !important;
}

.chatbot-toggle button:hover {
  transform: translateY(-2px) scale(1.05) !important;
  box-shadow: 0 16px 48px rgba(9, 47, 87, 0.5) !important;
  background: linear-gradient(135deg, #1976d2 0%, #092f57 100%) !important;
}

.chatbot-toggle button:active {
  transform: translateY(0) scale(1.02) !important;
  transition: all 0.1s ease !important;
}

/* Stable pulse animation for new messages - opacity only */
@keyframes pulse-glow {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
  100% {
    opacity: 1;
  }
}

.chatbot-toggle.has-new-message button {
  animation: pulse-glow 2s infinite;
}

/* Speech animation */
@keyframes speech-pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
  100% {
    opacity: 1;
  }
}

/* Chat Window Animations */
.chat-window-enter {
  opacity: 0;
  transform: scale(0.8) translateY(20px);
}

.chat-window-enter-active {
  opacity: 1;
  transform: scale(1) translateY(0);
  transition: all 300ms cubic-bezier(0.4, 0, 0.2, 1);
}

.chat-window-exit {
  opacity: 1;
  transform: scale(1) translateY(0);
}

.chat-window-exit-active {
  opacity: 0;
  transform: scale(0.8) translateY(20px);
  transition: all 300ms cubic-bezier(0.4, 0, 0.2, 1);
}

/* Message Animations */
.message-enter {
  opacity: 0;
  transform: translateY(10px);
}

.message-enter-active {
  opacity: 1;
  transform: translateY(0);
  transition: all 300ms ease-out;
}

/* Typing Indicator */
.typing-indicator {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 8px 12px;
  background: #f1f5f9;
  border-radius: 12px;
  margin: 8px 0;
}

.typing-dot {
  width: 6px;
  height: 6px;
  background: #64748b;
  border-radius: 50%;
  animation: typing 1.4s infinite ease-in-out;
}

.typing-dot:nth-child(1) {
  animation-delay: -0.32s;
}

.typing-dot:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes typing {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Scrollbar Styling */
.chat-messages::-webkit-scrollbar {
  width: 6px;
}

.chat-messages::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Message Bubble Styles - Legacy CSS (not used by React components) */
.message-bubble {
  position: relative;
  padding: 12px 16px;
  border-radius: 18px !important; /* More rounded corners */
  max-width: 75%; /* Reduced from 80% for better chatbot appearance */
  word-wrap: break-word;
  line-height: 1.3; /* Reduced line spacing from 1.4 to 1.3 */
}

.message-bubble.user {
  background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%) !important;
  color: white !important;
  margin-left: auto;
  border-radius: 18px !important; /* More rounded corners for user messages */
  border: 2px solid rgba(255, 255, 255, 0.3) !important; /* Stronger white border for visibility */
  box-shadow: 0 6px 25px rgba(25, 118, 210, 0.4) !important; /* Stronger shadow for visibility */
}

.message-bubble.assistant {
  background: #f8fafc !important;
  color: #1e293b !important;
  border: 1px solid #e2e8f0;
  border-radius: 18px !important; /* More rounded corners for AI messages */
}

.message-bubble.error {
  background: #ffebee !important;
  color: #c62828 !important;
  border: 1px solid #ffcdd2;
  border-radius: 18px !important;
}

/* Remove global Paper styling - handled by specific rules below */

/* Force user message styling for better visibility */
.chatbot-container .MuiPaper-root[style*="linear-gradient"] {
  background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%) !important;
  color: white !important;
  border: 2px solid rgba(255, 255, 255, 0.3) !important;
  border-radius: 18px !important;
}

.chatbot-container .MuiPaper-root[style*="linear-gradient"] * {
  color: white !important;
}

/* Additional overrides for user messages */
.chatbot-container .MuiPaper-root[style*="#1976d2"] {
  background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%) !important;
  color: white !important;
  border: 2px solid rgba(255, 255, 255, 0.3) !important;
  border-radius: 18px !important;
}

.chatbot-container .MuiPaper-root[style*="#1976d2"] .MuiTypography-root {
  color: white !important;
}

/* Ensure all message bubbles have rounded corners */
.chatbot-container .MuiPaper-root {
  border-radius: 18px !important;
}

/* Override any conflicting Material-UI styles */
.chatbot-container .MuiPaper-root.MuiPaper-elevation1,
.chatbot-container .MuiPaper-root.MuiPaper-elevation3 {
  border-radius: 18px !important;
}

/* Chart Container */
.chart-container {
  background: white;
  border-radius: 12px;
  padding: 16px;
  margin: 8px 0;
  border: 1px solid #e2e8f0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Suggested Actions */
.suggested-actions {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  margin-top: 12px;
}

.suggested-action {
  background: #e3f2fd;
  color: #1976d2;
  border: 1px solid #bbdefb;
  border-radius: 16px;
  padding: 4px 12px;
  font-size: 0.75rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.suggested-action:hover {
  background: #bbdefb;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(25, 118, 210, 0.2);
}

/* Quick Actions */
.quick-actions {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  margin-bottom: 12px;
}

.quick-action {
  background: #f1f5f9;
  color: #475569;
  border: 1px solid #e2e8f0;
  border-radius: 16px; /* Made rounder to match suggested actions */
  padding: 4px 10px;
  font-size: 0.75rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.quick-action:hover {
  background: #e2e8f0;
  border-color: #cbd5e1;
}

.quick-action:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Input Field */
.chat-input {
  border-radius: 12px;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  transition: all 0.2s ease;
}

.chat-input:focus {
  border-color: #1976d2;
  box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.1);
}

/* Voice Recording Indicator */
.recording-indicator {
  background: #fee2e2;
  border: 1px solid #fecaca;
  border-radius: 8px;
  padding: 8px 12px;
  margin-top: 8px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.recording-dot {
  width: 8px;
  height: 8px;
  background: #dc2626;
  border-radius: 50%;
  animation: pulse-red 1s infinite;
}

@keyframes pulse-red {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

/* Speech Recognition States */
.speech-listening {
  background: linear-gradient(45deg, #fee2e2, #fef3c7) !important;
  border-color: #fbbf24 !important;
  animation: listening-pulse 1.5s infinite;
}

@keyframes listening-pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Text-to-Speech Indicator */
.speaking-indicator {
  background: #e0f2fe;
  border: 1px solid #81d4fa;
  border-radius: 8px;
  padding: 4px 8px;
  margin-left: 8px;
  display: inline-flex;
  align-items: center;
  gap: 4px;
  font-size: 0.75rem;
  color: #0277bd;
}

.speaking-dot {
  width: 4px;
  height: 4px;
  background: #0277bd;
  border-radius: 50%;
  animation: speaking-wave 1s infinite ease-in-out;
}

.speaking-dot:nth-child(1) { animation-delay: 0s; }
.speaking-dot:nth-child(2) { animation-delay: 0.1s; }
.speaking-dot:nth-child(3) { animation-delay: 0.2s; }

@keyframes speaking-wave {
  0%, 60%, 100% {
    transform: scaleY(1);
  }
  30% {
    transform: scaleY(2);
  }
}

/* Fullscreen Mode */
.chatbot-container.fullscreen {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  border-radius: 0 !important;
  border: none !important;
  z-index: 10000 !important;
}

/* Fullscreen mode styling is handled by inline styles in the component */

/* Mobile Responsive */
@media (max-width: 600px) {
  .chatbot-container {
    /* Let inline styles handle positioning */
    transform: none !important;
  }

  .chatbot-toggle {
    bottom: 16px;
    right: 16px;
  }

  .message-bubble {
    max-width: 85%; /* Slightly reduced for better mobile chatbot appearance */
  }

  .suggested-actions,
  .quick-actions {
    gap: 4px;
  }

  .suggested-action,
  .quick-action {
    font-size: 0.7rem;
    padding: 3px 8px;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .message-bubble.assistant {
    background: #1e293b;
    color: #f1f5f9;
    border-color: #334155;
  }
  
  .chart-container {
    background: #1e293b;
    border-color: #334155;
  }
  
  .chat-input {
    background: #1e293b;
    border-color: #334155;
    color: #f1f5f9;
  }
  
  .quick-action {
    background: #334155;
    color: #cbd5e1;
    border-color: #475569;
  }
  
  .quick-action:hover {
    background: #475569;
    border-color: #64748b;
  }
}

/* Enhanced Animation Keyframes */
@keyframes chatSlideIn {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes messageSlideIn {
  from {
    opacity: 0;
    transform: translateX(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateX(0) scale(1);
  }
}

@keyframes userMessageSlideIn {
  from {
    opacity: 0;
    transform: translateX(20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateX(0) scale(1);
  }
}

@keyframes typingDots {
  0%, 60%, 100% {
    transform: translateY(0);
    opacity: 0.4;
  }
  30% {
    transform: translateY(-8px);
    opacity: 1;
  }
}

@keyframes chartEntrance {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes quickQuestionHover {
  from {
    transform: translateY(0) scale(1);
  }
  to {
    transform: translateY(-2px) scale(1.02);
  }
}

@keyframes chatGlow {
  0%, 100% {
    box-shadow: 0 8px 32px rgba(9, 47, 87, 0.3);
  }
  50% {
    box-shadow: 0 12px 40px rgba(25, 118, 210, 0.4);
  }
}

/* Enhanced Animation Classes */
.chat-slide-in {
  animation: chatSlideIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.message-slide-in {
  animation: messageSlideIn 0.35s cubic-bezier(0.4, 0, 0.2, 1);
}

.user-message-slide-in {
  animation: userMessageSlideIn 0.35s cubic-bezier(0.4, 0, 0.2, 1);
}

.typing-dots {
  animation: typingDots 0.8s infinite ease-in-out;
}

.chart-entrance {
  animation: chartEntrance 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.quick-question-hover {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.quick-question-hover:hover {
  animation: quickQuestionHover 0.2s ease-out forwards;
}

.chat-glow {
  animation: chatGlow 3s ease-in-out infinite;
}

/* Accessibility */
.chatbot-container button:focus,
.suggested-action:focus,
.quick-action:focus {
  outline: 2px solid #1976d2;
  outline-offset: 2px;
}

/* Prevent page scroll interference */
.chatbot-container,
.chatbot-toggle {
  /* Ensure chatbot elements don't cause page scroll */
  isolation: isolate;
}

/* Prevent body scroll when chatbot is focused */
.chatbot-container:focus-within {
  /* Keep focus within chatbot */
  contain: layout style;
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .chatbot-toggle button,
  .message-bubble,
  .suggested-action,
  .quick-action {
    transition: none;
  }

  .typing-dot,
  .recording-dot {
    animation: none;
  }

  .chatbot-toggle.has-new-message button {
    animation: none;
  }
}
