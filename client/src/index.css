/* Modern SafetyConnect Styles */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

* {
  box-sizing: border-box;
}

body {
  margin: 0;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f8fafc;
  font-weight: 400;
  line-height: 1.5;
}

code {
  font-family: 'SF Mono', source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

/* Modern scrollbar - Light mode */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: rgba(9, 47, 87, 0.3);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(9, 47, 87, 0.5);
}

/* Dark mode scrollbar */
[data-theme="dark"] ::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] ::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
}

[data-theme="dark"] ::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.4);
}

/* Modern focus styles */
*:focus-visible {
  outline: 2px solid rgba(9, 47, 87, 0.5);
  outline-offset: 2px;
}

[data-theme="dark"] *:focus-visible {
  outline: 2px solid rgba(59, 130, 246, 0.5);
  outline-offset: 2px;
}

/* Smooth transitions for all elements */
* {
  transition: color 0.2s ease, background-color 0.2s ease, border-color 0.2s ease,
              box-shadow 0.2s ease, transform 0.2s ease;
}

/* Modern selection styles - Light mode */
::selection {
  background-color: rgba(9, 47, 87, 0.2);
  color: #092f57;
}

::-moz-selection {
  background-color: rgba(9, 47, 87, 0.2);
  color: #092f57;
}

/* Dark mode selection styles */
[data-theme="dark"] ::selection {
  background-color: rgba(255, 255, 255, 0.2);
  color: #ffffff;
}

[data-theme="dark"] ::-moz-selection {
  background-color: rgba(255, 255, 255, 0.2);
  color: #ffffff;
}

/* Ensure proper cursor visibility in dark mode */
[data-theme="dark"] input,
[data-theme="dark"] textarea {
  caret-color: #ffffff;
}

/* Dark mode body background */
[data-theme="dark"] body {
  background-color: #0f172a;
  color: #f1f5f9;
}

/* AI Insights Loading Animation */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.5;
    transform: scale(1.1);
  }
}
