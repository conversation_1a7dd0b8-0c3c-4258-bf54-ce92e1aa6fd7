/**
 * Main App Component
 * Modern Safety Dashboard - Comprehensive Safety Management System
 */

import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';
import SimpleAIAnalysisPage from './pages/SimpleAIAnalysisPage';
import UnifiedSafetyDashboard from './pages/UnifiedSafetyDashboard';
import FileUploadPage from './pages/FileUploadPage';
import DataHealthPage from './pages/DataHealthPage';
import AIInsightsTest from './components/test/AIInsightsTest';
import DashboardTest from './components/test/DashboardTest';
import NotificationTest from './components/test/NotificationTest';
import './styles/globals.css';

// Import chart manager to initialize it globally
import './services/chartManager';

// Modern Safety Dashboard Theme with Custom Blue
const theme = createTheme({
  palette: {
    mode: 'light',
    primary: {
      main: '#092f57', // Custom SafetyConnect Blue
      light: '#1a4a7a',
      dark: '#061f3a',
    },
    secondary: {
      main: '#f97316', // Safety Orange - Alerts, warnings
      light: '#fb923c',
      dark: '#ea580c',
    },
    success: {
      main: '#059669', // Green - Completed, safe
      light: '#10b981',
      dark: '#047857',
    },
    warning: {
      main: '#d97706', // Amber - Attention needed
      light: '#f59e0b',
      dark: '#b45309',
    },
    error: {
      main: '#dc2626', // Red - Critical issues
      light: '#ef4444',
      dark: '#b91c1c',
    },
    background: {
      default: '#f8fafc',
      paper: '#ffffff',
    },
    text: {
      primary: '#1f2937',
      secondary: '#6b7280',
    },
  },

  typography: {
    fontFamily: '"Inter", "Roboto", "Helvetica", "Arial", sans-serif',
    h1: {
      fontSize: '2.5rem',
      fontWeight: 700,
      lineHeight: 1.2,
    },
    h2: {
      fontSize: '2rem',
      fontWeight: 600,
      lineHeight: 1.3,
    },
    h3: {
      fontSize: '1.5rem',
      fontWeight: 600,
      lineHeight: 1.4,
    },
    h4: {
      fontSize: '1.25rem',
      fontWeight: 600,
      lineHeight: 1.4,
    },
    h5: {
      fontSize: '1.125rem',
      fontWeight: 600,
      lineHeight: 1.4,
    },
    h6: {
      fontSize: '1rem',
      fontWeight: 600,
      lineHeight: 1.4,
    },
    body1: {
      fontSize: '1rem',
      lineHeight: 1.6,
    },
    body2: {
      fontSize: '0.875rem',
      lineHeight: 1.6,
    },
  },
  shape: {
    borderRadius: 6, // Reduced global border radius for cleaner look
  },
  components: {
    MuiCard: {
      styleOverrides: {
        root: {
          borderRadius: 12,
          boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
          border: '1px solid #e5e7eb',
          transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
          '&:hover': {
            boxShadow: '0 8px 25px 0 rgba(0, 0, 0, 0.15)',
            transform: 'translateY(-2px)',
          },
        },
      },
    },
    MuiButton: {
      styleOverrides: {
        root: {
          textTransform: 'none',
          fontWeight: 600,
          borderRadius: 8,
          transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
          '&:hover': {
            transform: 'translateY(-1px)',
            boxShadow: '0 4px 12px 0 rgba(0, 0, 0, 0.15)',
          },
        },
      },
    },
    MuiPaper: {
      styleOverrides: {
        root: {
          borderRadius: 12,
          transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
        },
      },
    },
    MuiChip: {
      styleOverrides: {
        root: {
          borderRadius: 16,
          transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
          '&:hover': {
            transform: 'translateY(-1px)',
            boxShadow: '0 2px 8px 0 rgba(0, 0, 0, 0.1)',
          },
        },
      },
    },
    MuiTextField: {
      styleOverrides: {
        root: {
          '& .MuiOutlinedInput-root': {
            borderRadius: 4, // Reduced border radius
            transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
            '&:hover': {
              boxShadow: '0 2px 8px 0 rgba(0, 0, 0, 0.1)',
            },
          },
        },
      },
    },
  },
});

function App() {

  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <Router>
        <div className="App">
          <Routes>
            <Route path="/" element={<FileUploadPage />} />
            <Route path="/dashboard" element={<UnifiedSafetyDashboard />} />
            <Route path="/data-health" element={<DataHealthPage />} />
            <Route path="/ai-analysis" element={<SimpleAIAnalysisPage />} />
            <Route path="/test-ai" element={<AIInsightsTest />} />
            <Route path="/test-dashboard" element={<DashboardTest />} />
            <Route path="/test-notifications" element={<NotificationTest />} />
          </Routes>
        </div>
      </Router>
    </ThemeProvider>
  );
}

export default App;
